package database

import (
	"context"
	"errors"
	"time"

	"github.com/eldon111/impactresume/internal/models"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type Database interface {
	Collection(name string) Collection
	Close(ctx context.Context) error
}

type Collection interface {
	InsertOne(ctx context.Context, document interface{}) (*mongo.InsertOneResult, error)
	FindOne(ctx context.Context, filter interface{}) *mongo.SingleResult
	Find(ctx context.Context, filter interface{}) (*mongo.Cursor, error)
	UpdateOne(ctx context.Context, filter interface{}, update interface{}) (*mongo.UpdateResult, error)
	DeleteOne(ctx context.Context, filter interface{}) (*mongo.DeleteResult, error)
	Aggregate(ctx context.Context, pipeline interface{}) (*mongo.Cursor, error)
}

type MongoDB struct {
	client   *mongo.Client
	database *mongo.Database
}

func NewMongoDB(client *mongo.Client, dbName string) *MongoDB {
	return &MongoDB{
		client:   client,
		database: client.Database(dbName),
	}
}

func (m *MongoDB) Collection(name string) Collection {
	return &MongoCollection{
		collection: m.database.Collection(name),
	}
}

func (m *MongoDB) Close(ctx context.Context) error {
	return m.client.Disconnect(ctx)
}

type MongoCollection struct {
	collection *mongo.Collection
}

func (c *MongoCollection) InsertOne(ctx context.Context, document interface{}) (*mongo.InsertOneResult, error) {
	return c.collection.InsertOne(ctx, document)
}

func (c *MongoCollection) FindOne(ctx context.Context, filter interface{}) *mongo.SingleResult {
	return c.collection.FindOne(ctx, filter)
}

func (c *MongoCollection) Find(ctx context.Context, filter interface{}) (*mongo.Cursor, error) {
	return c.collection.Find(ctx, filter)
}

func (c *MongoCollection) UpdateOne(ctx context.Context, filter interface{}, update interface{}) (*mongo.UpdateResult, error) {
	return c.collection.UpdateOne(ctx, filter, update)
}

func (c *MongoCollection) DeleteOne(ctx context.Context, filter interface{}) (*mongo.DeleteResult, error) {
	return c.collection.DeleteOne(ctx, filter)
}

func (c *MongoCollection) Aggregate(ctx context.Context, pipeline interface{}) (*mongo.Cursor, error) {
	return c.collection.Aggregate(ctx, pipeline)
}

// Job-specific database operations
var (
	ErrJobNotFound = errors.New("job not found")
)

// JobRepository defines the interface for job database operations
type JobRepository interface {
	CreateJob(ctx context.Context, job *models.Job) error
	GetJobByID(ctx context.Context, userID int, id string) (*models.Job, error)
	GetJobs(ctx context.Context, userID int) ([]models.Job, error)
	UpdateJobDescription(ctx context.Context, userID int, jobID string, description string) error
	DeleteJob(ctx context.Context, userID int, jobID string) error
}

func (m *MongoDB) GetJobByID(ctx context.Context, userID int, jobID string) (*models.Job, error) {
	collection := m.Collection("jobs")
	filter := bson.M{
		"id":     jobID,
		"userId": userID,
	}

	var job models.Job
	err := collection.FindOne(ctx, filter).Decode(&job)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, ErrJobNotFound
		}
		return nil, err
	}
	return &job, nil
}

func (m *MongoDB) GetJobs(ctx context.Context, userID int) ([]models.Job, error) {
	collection := m.Collection("jobs")
	filter := bson.M{"userId": userID}

	cursor, err := collection.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var jobs []models.Job
	if err = cursor.All(ctx, &jobs); err != nil {
		return nil, err
	}
	return jobs, nil
}

func (m *MongoDB) CreateJob(ctx context.Context, job *models.Job) error {
	collection := m.Collection("jobs")
	_, err := collection.InsertOne(ctx, job)
	return err
}

func (m *MongoDB) UpdateJobDescription(ctx context.Context, userID int, jobID string, description string) error {
	collection := m.Collection("jobs")
	filter := bson.M{
		"id":     jobID,
		"userId": userID,
	}
	update := bson.M{
		"$set": bson.M{
			"description": description,
			"updatedAt":   time.Now(),
		},
	}

	result, err := collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	if result.MatchedCount == 0 {
		return ErrJobNotFound
	}
	return nil
}

func (m *MongoDB) DeleteJob(ctx context.Context, userID int, jobID string) error {
	collection := m.Collection("jobs")
	filter := bson.M{
		"id":     jobID,
		"userId": userID,
	}

	result, err := collection.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	if result.DeletedCount == 0 {
		return ErrJobNotFound
	}
	return nil
}

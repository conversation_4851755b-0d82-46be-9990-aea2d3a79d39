package handlers

import (
	"net/http"

	"github.com/eldon111/impactresume/internal/services"
	"github.com/golang-jwt/jwt/v5"
	"github.com/labstack/echo/v4"
)

const (
	// UserIDKey is the key used to store user ID in Echo context
	UserIDKey = "user_id"
	// UserKey is the key used to store user claims in Echo context
	UserKey = "user"
)

// UserMiddleware extracts user information from JWT token and adds it to context
func UserMiddleware() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Get the JWT token from the context (set by echo-jwt middleware)
			user := c.Get("user")
			if user == nil {
				return echo.NewHTTPError(http.StatusUnauthorized, "No user token found")
			}

			// Extract JWT token
			token, ok := user.(*jwt.Token)
			if !ok {
				return echo.NewHTTPError(http.StatusUnauthorized, "Invalid token type")
			}

			// Extract claims
			claims, ok := token.Claims.(jwt.MapClaims)
			if !ok {
				return echo.NewHTTPError(http.StatusUnauthorized, "Invalid token claims")
			}

			// Extract user ID from claims
			userIDFloat, ok := claims["userId"].(float64)
			if !ok {
				return echo.NewHTTPError(http.StatusUnauthorized, "Missing user ID in token")
			}

			userID := int(userIDFloat)

			// Add user ID to context for easy access
			c.Set(UserIDKey, userID)

			// Also store full claims for additional user info if needed
			userClaims := &services.JWTClaims{
				UserID:   userID,
				Email:    getStringFromClaims(claims, "email"),
				Provider: getStringFromClaims(claims, "provider"),
			}
			c.Set(UserKey, userClaims)

			return next(c)
		}
	}
}

// GetUserID extracts the user ID from Echo context
func GetUserID(c echo.Context) (int, error) {
	userID := c.Get(UserIDKey)
	if userID == nil {
		return 0, echo.NewHTTPError(http.StatusUnauthorized, "User not authenticated")
	}

	id, ok := userID.(int)
	if !ok {
		return 0, echo.NewHTTPError(http.StatusUnauthorized, "Invalid user ID type")
	}

	return id, nil
}

// GetUserClaims extracts the user claims from Echo context
func GetUserClaims(c echo.Context) (*services.JWTClaims, error) {
	user := c.Get(UserKey)
	if user == nil {
		return nil, echo.NewHTTPError(http.StatusUnauthorized, "User not authenticated")
	}

	claims, ok := user.(*services.JWTClaims)
	if !ok {
		return nil, echo.NewHTTPError(http.StatusUnauthorized, "Invalid user claims type")
	}

	return claims, nil
}

// Helper function to safely extract string values from JWT claims
func getStringFromClaims(claims jwt.MapClaims, key string) string {
	if value, exists := claims[key]; exists {
		if strValue, ok := value.(string); ok {
			return strValue
		}
	}
	return ""
}

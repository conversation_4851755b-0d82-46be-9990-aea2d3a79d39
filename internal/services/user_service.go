package services

import (
	"context"
	"fmt"
	"time"

	"github.com/eldon111/impactresume/internal/database"
	"github.com/eldon111/impactresume/internal/models"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type UserService struct {
	db database.Database
}

func NewUserService(db database.Database) *UserService {
	return &UserService{
		db: db,
	}
}

func (us *UserService) CreateOrUpdateGitHubUser(ctx context.Context, githubOAuth *models.GitHubOAuth) (*models.User, error) {
	collection := us.db.Collection("users")

	// First, try to find existing user with this GitHub ID
	var existingUser models.User
	err := collection.FindOne(ctx, bson.M{"githubOauth.githubId": githubOAuth.GitHubID}).Decode(&existingUser)

	if err == nil {
		// User exists, update GitHub OAuth data
		now := time.Now()
		githubOAuth.UserID = existingUser.ID
		githubOAuth.UpdatedAt = now
		githubOAuth.CreatedAt = existingUser.GitHubOAuth.CreatedAt // Preserve original creation time

		existingUser.GitHubOAuth = githubOAuth
		existingUser.UpdatedAt = now

		// Update the user document
		_, err = collection.UpdateOne(ctx,
			bson.M{"_id": existingUser.ID},
			bson.M{"$set": bson.M{
				"githubOauth": githubOAuth,
				"updatedAt":   now,
			}})

		if err != nil {
			return nil, fmt.Errorf("failed to update existing user: %w", err)
		}

		return &existingUser, nil
	} else if err != mongo.ErrNoDocuments {
		return nil, fmt.Errorf("failed to query for existing user: %w", err)
	}

	// User doesn't exist, create new one
	userID, err := us.generateUserID(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to generate user ID: %w", err)
	}

	now := time.Now()
	githubOAuth.UserID = userID
	githubOAuth.CreatedAt = now
	githubOAuth.UpdatedAt = now

	user := &models.User{
		ID:          userID,
		CreatedAt:   now,
		UpdatedAt:   now,
		GitHubOAuth: githubOAuth,
	}

	_, err = collection.InsertOne(ctx, user)
	if err != nil {
		return nil, fmt.Errorf("failed to create new user: %w", err)
	}

	return user, nil
}

func (us *UserService) CreateOrUpdateGoogleUser(ctx context.Context, googleOAuth *models.GoogleOAuth) (*models.User, error) {
	collection := us.db.Collection("users")

	// First, try to find existing user with this Google ID
	var existingUser models.User
	err := collection.FindOne(ctx, bson.M{"googleOauth.googleId": googleOAuth.GoogleID}).Decode(&existingUser)

	if err == nil {
		// User exists, update Google OAuth data
		now := time.Now()
		googleOAuth.UserID = existingUser.ID
		googleOAuth.UpdatedAt = now
		googleOAuth.CreatedAt = existingUser.GoogleOAuth.CreatedAt // Preserve original creation time

		existingUser.GoogleOAuth = googleOAuth
		existingUser.UpdatedAt = now

		// Update the user document
		_, err = collection.UpdateOne(ctx,
			bson.M{"_id": existingUser.ID},
			bson.M{"$set": bson.M{
				"googleOauth": googleOAuth,
				"updatedAt":   now,
			}})

		if err != nil {
			return nil, fmt.Errorf("failed to update existing user: %w", err)
		}

		return &existingUser, nil
	} else if err != mongo.ErrNoDocuments {
		return nil, fmt.Errorf("failed to query for existing user: %w", err)
	}

	// User doesn't exist, create new one
	userID, err := us.generateUserID(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to generate user ID: %w", err)
	}

	now := time.Now()
	googleOAuth.UserID = userID
	googleOAuth.CreatedAt = now
	googleOAuth.UpdatedAt = now

	user := &models.User{
		ID:          userID,
		CreatedAt:   now,
		UpdatedAt:   now,
		GoogleOAuth: googleOAuth,
	}

	_, err = collection.InsertOne(ctx, user)
	if err != nil {
		return nil, fmt.Errorf("failed to create new user: %w", err)
	}

	return user, nil
}

func (us *UserService) CreateOrUpdateLinkedInUser(ctx context.Context, linkedinOAuth *models.LinkedInOAuth) (*models.User, error) {
	collection := us.db.Collection("users")

	// First, try to find existing user with this LinkedIn ID
	var existingUser models.User
	err := collection.FindOne(ctx, bson.M{"linkedinOauth.linkedinId": linkedinOAuth.LinkedInID}).Decode(&existingUser)

	if err == nil {
		// User exists, update LinkedIn OAuth data
		now := time.Now()
		linkedinOAuth.UserID = existingUser.ID
		linkedinOAuth.UpdatedAt = now
		linkedinOAuth.CreatedAt = existingUser.LinkedInOAuth.CreatedAt // Preserve original creation time

		existingUser.LinkedInOAuth = linkedinOAuth
		existingUser.UpdatedAt = now

		// Update the user document
		_, err = collection.UpdateOne(ctx,
			bson.M{"_id": existingUser.ID},
			bson.M{"$set": bson.M{
				"linkedinOauth": linkedinOAuth,
				"updatedAt":     now,
			}})

		if err != nil {
			return nil, fmt.Errorf("failed to update existing user: %w", err)
		}

		return &existingUser, nil
	} else if err != mongo.ErrNoDocuments {
		return nil, fmt.Errorf("failed to query for existing user: %w", err)
	}

	// User doesn't exist, create new one
	userID, err := us.generateUserID(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to generate user ID: %w", err)
	}

	now := time.Now()
	linkedinOAuth.UserID = userID
	linkedinOAuth.CreatedAt = now
	linkedinOAuth.UpdatedAt = now

	user := &models.User{
		ID:            userID,
		CreatedAt:     now,
		UpdatedAt:     now,
		LinkedInOAuth: linkedinOAuth,
	}

	_, err = collection.InsertOne(ctx, user)
	if err != nil {
		return nil, fmt.Errorf("failed to create new user: %w", err)
	}

	return user, nil
}

func (us *UserService) GetUserByID(ctx context.Context, userID int) (*models.User, error) {
	collection := us.db.Collection("users")

	var user models.User
	err := collection.FindOne(ctx, bson.M{"_id": userID}).Decode(&user)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	return &user, nil
}

func (us *UserService) generateUserID(ctx context.Context) (int, error) {
	collection := us.db.Collection("users")

	// Find the highest existing user ID
	pipeline := []bson.M{
		{"$group": bson.M{
			"_id":   nil,
			"maxId": bson.M{"$max": "$_id"},
		}},
	}

	cursor, err := collection.Aggregate(ctx, pipeline)
	if err != nil {
		return 0, fmt.Errorf("failed to query max user ID: %w", err)
	}
	defer cursor.Close(ctx)

	var result struct {
		MaxID int `bson:"maxId"`
	}

	if cursor.Next(ctx) {
		if err := cursor.Decode(&result); err != nil {
			return 0, fmt.Errorf("failed to decode max user ID: %w", err)
		}
		return result.MaxID + 1, nil
	}

	// No existing users, start with ID 1
	return 1, nil
}

package services

import (
	"fmt"
	"time"

	"github.com/eldon111/impactresume/internal/config"
	"github.com/eldon111/impactresume/internal/models"
	"github.com/golang-jwt/jwt/v5"
)

// Simplified JWT service - only handles token generation
// Echo handles validation automatically
type JWTService struct {
	config *config.JWTConfig
}

type JWTClaims struct {
	UserID   int    `json:"userId"`
	Email    string `json:"email"`
	Provider string `json:"provider"`
	jwt.RegisteredClaims
}

func NewJWTService(config *config.JWTConfig) *JWTService {
	return &JWTService{
		config: config,
	}
}

func (j *JWTService) GenerateToken(user *models.User) (string, error) {
	var email, provider string

	if user.GitHubOAuth != nil {
		provider = "github"
		if user.GitHubOAuth.Email != nil {
			email = *user.GitHubOAuth.Email
		}
	} else if user.GoogleOAuth != nil {
		provider = "google"
		email = user.GoogleOAuth.Email
	} else if user.LinkedInOAuth != nil {
		provider = "linkedin"
		email = user.LinkedInOAuth.Email
	}

	claims := JWTClaims{
		UserID:   user.ID,
		Email:    email,
		Provider: provider,
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    j.config.Issuer,
			Subject:   fmt.Sprintf("%d", user.ID),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(j.config.Expiration)),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(j.config.Secret))
}

func (j *JWTService) RefreshToken(tokenString string) (string, error) {
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(j.config.Secret), nil
	})

	if err != nil {
		return "", fmt.Errorf("failed to parse token: %w", err)
	}

	if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
		// Create new token with updated expiration
		newClaims := JWTClaims{
			UserID:   claims.UserID,
			Email:    claims.Email,
			Provider: claims.Provider,
			RegisteredClaims: jwt.RegisteredClaims{
				Issuer:    j.config.Issuer,
				Subject:   claims.Subject,
				IssuedAt:  jwt.NewNumericDate(time.Now()),
				ExpiresAt: jwt.NewNumericDate(time.Now().Add(j.config.Expiration)),
			},
		}

		newToken := jwt.NewWithClaims(jwt.SigningMethodHS256, newClaims)
		return newToken.SignedString([]byte(j.config.Secret))
	}

	return "", fmt.Errorf("invalid token")
}

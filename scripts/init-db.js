// MongoDB Database Initialization Script
// This script initializes the impactresume database with collections and indexes
// Run with: mongosh impactresume scripts/init-db.js

print('🚀 Initializing ImpactResume Database...\n');

// Switch to impactresume database
use('impactresume');

print('📊 Creating collections...');

// Drop existing collections if they exist (for clean initialization)
if (db.users.exists()) {
  print('  Dropping existing users collection...');
  db.users.drop();
}
if (db.jobs.exists()) {
  print('  Dropping existing jobs collection...');
  db.jobs.drop();
}

// Create users collection with validation
db.createCollection('users', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['_id', 'createdAt', 'updatedAt'],
      properties: {
        _id: {
          bsonType: 'int',
          description: 'User ID must be an integer'
        },
        createdAt: {
          bsonType: 'date',
          description: 'Creation timestamp must be a date'
        },
        updatedAt: {
          bsonType: 'date',
          description: 'Update timestamp must be a date'
        },
        githubOauth: {
          bsonType: 'object',
          properties: {
            githubId: {
              bsonType: 'string',
              description: 'GitHub ID must be a string'
            },
            username: {
              bsonType: 'string',
              description: 'GitHub username must be a string'
            }
          }
        },
        googleOauth: {
          bsonType: 'object',
          properties: {
            googleId: {
              bsonType: 'string',
              description: 'Google ID must be a string'
            },
            email: {
              bsonType: 'string',
              description: 'Google email must be a string'
            }
          }
        }
      }
    }
  }
});

// Create jobs collection with validation
db.createCollection('jobs', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['_id', 'userId', 'title', 'company', 'startDate', 'description', 'createdAt', 'updatedAt'],
      properties: {
        _id: {
          bsonType: 'string',
          description: 'Job ID must be a string'
        },
        userId: {
          bsonType: 'string',
          description: 'User ID must be a string'
        },
        title: {
          bsonType: 'string',
          minLength: 1,
          maxLength: 200,
          description: 'Job title must be 1-200 characters'
        },
        company: {
          bsonType: 'string',
          minLength: 1,
          maxLength: 200,
          description: 'Company name must be 1-200 characters'
        },
        startDate: {
          bsonType: 'date',
          description: 'Start date must be a date'
        },
        endDate: {
          bsonType: ['date', 'null'],
          description: 'End date must be a date or null'
        },
        isCurrent: {
          bsonType: 'bool',
          description: 'Current job flag must be boolean'
        },
        description: {
          bsonType: 'string',
          maxLength: 5000,
          description: 'Job description must be max 5000 characters'
        },
        createdAt: {
          bsonType: 'date',
          description: 'Creation timestamp must be a date'
        },
        updatedAt: {
          bsonType: 'date',
          description: 'Update timestamp must be a date'
        }
      }
    }
  }
});

print('✅ Collections created successfully\n');

print('🔍 Creating indexes...');

// Users collection indexes
print('  Creating users indexes...');
db.users.createIndex({ _id: 1 }, { name: 'idx_users_id' });
db.users.createIndex({ 'githubOauth.githubId': 1 }, { unique: true, sparse: true, name: 'idx_users_github_id' });
db.users.createIndex({ 'googleOauth.googleId': 1 }, { unique: true, sparse: true, name: 'idx_users_google_id' });
db.users.createIndex({ createdAt: 1 }, { name: 'idx_users_created_at' });
db.users.createIndex({ updatedAt: 1 }, { name: 'idx_users_updated_at' });

// Jobs collection indexes
print('  Creating jobs indexes...');
db.jobs.createIndex({ _id: 1 }, { name: 'idx_jobs_id' });
db.jobs.createIndex({ userId: 1 }, { name: 'idx_jobs_user_id' });
db.jobs.createIndex({ createdAt: 1 }, { name: 'idx_jobs_created_at' });
db.jobs.createIndex({ updatedAt: 1 }, { name: 'idx_jobs_updated_at' });
db.jobs.createIndex({ isCurrent: 1 }, { name: 'idx_jobs_is_current' });

// Compound indexes for common queries
print('  Creating compound indexes...');
db.jobs.createIndex({ userId: 1, createdAt: -1 }, { name: 'idx_jobs_user_created_desc' });
db.jobs.createIndex({ userId: 1, isCurrent: 1 }, { name: 'idx_jobs_user_current' });

print('✅ Indexes created successfully\n');

print('📋 Database initialization summary:');
print('  Database: impactresume');
print('  Collections: users, jobs');
print('  Users indexes: ' + db.users.getIndexes().length);
print('  Jobs indexes: ' + db.jobs.getIndexes().length);

print('\n🎉 Database initialization complete!');
print('📄 View collections: show collections');
print('🔍 View indexes: db.users.getIndexes() or db.jobs.getIndexes()');